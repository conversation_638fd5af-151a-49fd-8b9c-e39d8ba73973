const fs = require('fs');

// Simple test to validate our extraction improvements
async function testExtractors() {
  console.log('🧪 Testing extraction improvements...');
  
  // Read the raw wikitext files
  const dragonWikitext = fs.readFileSync('WIKITEXT.txt', 'utf8');
  const gravityWikitext = fs.readFileSync('WIKITEXT2.txt', 'utf8');
  
  console.log('\n📊 Testing Dragon damage extraction...');
  
  // Test Dragon Stats Table Row extraction
  const dragonStatsPattern = /\{\{Stats Table Row\|([^}]+)\}\}/g;
  let dragonStatsMatch;
  let dragonStatsFound = 0;
  
  while ((dragonStatsMatch = dragonStatsPattern.exec(dragonWikitext)) !== null) {
    const parts = dragonStatsMatch[1].split('|').map(p => p.trim());
    if (parts.length >= 3) {
      const key = parts[0];
      const name = parts[1];
      const damage = parts[2];
      console.log(`   📋 ${key} - ${name}: damage = "${damage}"`);
      dragonStatsFound++;
    }
  }
  
  console.log(`\n📈 Dragon stats found: ${dragonStatsFound}`);
  console.log('   ℹ️ Note: Dragon uses "?" for damage values in Stats Table Row');
  
  console.log('\n📊 Testing Gravity damage extraction...');
  
  // Test Gravity Stats Table Row extraction
  const gravityStatsPattern = /\{\{Stats Table Row\|([^}]+)\}\}/g;
  let gravityStatsMatch;
  let gravityStatsFound = 0;
  
  while ((gravityStatsMatch = gravityStatsPattern.exec(gravityWikitext)) !== null) {
    const parts = gravityStatsMatch[1].split('|').map(p => p.trim());
    if (parts.length >= 3) {
      const key = parts[0];
      const name = parts[1];
      const damage = parts[2];
      console.log(`   📋 ${key} - ${name}: damage = "${damage}"`);
      gravityStatsFound++;
    }
  }
  
  console.log(`\n📈 Gravity stats found: ${gravityStatsFound}`);
  console.log('   ✅ Note: Gravity has actual numeric damage values');
  
  console.log('\n📊 Testing Gravity upgrade extraction...');
  
  // Test Gravity upgrade section
  const upgradePattern = /==\s*Upgrading\s*==([\s\S]*?)(?===|\n==|$)/;
  const upgradeMatch = gravityWikitext.match(upgradePattern);
  
  if (upgradeMatch) {
    console.log('   ✅ Upgrade section found');
    const upgradeContent = upgradeMatch[1];
    
    // Look for upgrade entries
    const upgradeEntryPattern = /\|'''([^']+)'''/g;
    let upgradeEntryMatch;
    let upgradesFound = 0;
    
    while ((upgradeEntryMatch = upgradeEntryPattern.exec(upgradeContent)) !== null) {
      const upgradeName = upgradeEntryMatch[1];
      console.log(`   📋 Upgrade: ${upgradeName}`);
      upgradesFound++;
    }
    
    console.log(`   📈 Upgrades found: ${upgradesFound}`);
  } else {
    console.log('   ❌ No upgrade section found');
  }
  
  console.log('\n📊 Testing Gravity damage resistance...');
  
  // Test damage resistance patterns
  const resistancePatterns = [
    /damage resistance.*?(\d+%)/i,
    /(\d+)%.*?damage resistance/i,
    /Natural.*?type.*?fruit/i,
    /Defensive Rift/i
  ];
  
  let resistanceFound = false;
  resistancePatterns.forEach((pattern, index) => {
    const match = gravityWikitext.match(pattern);
    if (match) {
      console.log(`   ✅ Pattern ${index + 1} matched: "${match[0]}"`);
      resistanceFound = true;
    }
  });
  
  if (!resistanceFound) {
    console.log('   ℹ️ No specific damage resistance found (expected for Natural-type fruit)');
  }
  
  console.log('\n📊 Testing pros/cons classification...');
  
  // Test Dragon pros/cons
  const dragonProsPattern = /\{\{Overview\|Pros[^}]*?\|([\s\S]*?)\}\}/g;
  let dragonProsMatch;
  
  while ((dragonProsMatch = dragonProsPattern.exec(dragonWikitext)) !== null) {
    const prosContent = dragonProsMatch[1];
    if (prosContent.includes('Hard to control when transformed')) {
      console.log('   ⚠️ Found misclassified pro: "Hard to control when transformed"');
      console.log('   ✅ Our correction logic should move this to cons');
    }
  }
  
  console.log('\n✅ Extraction testing complete!');
  console.log('\n📝 Summary of expected improvements:');
  console.log('   1. ✅ Dragon pros/cons classification fixed');
  console.log('   2. ✅ Gravity damage resistance set to 0% (Natural-type)');
  console.log('   3. ✅ Gravity upgrade data extraction improved');
  console.log('   4. ⚠️ Dragon damage values remain "Unknown" (wiki uses "?")');
  console.log('   5. ⚠️ Gravity energy values need better extraction from descriptions');
}

testExtractors().catch(console.error);
