{"_id": {"$oid": "68794d0bfccb4c41ff7178df"}, "name": "Gravity", "category": "fruit", "description": "Gravity allows the user to control and manipulate the gravitational force in their environment, enabling them to influence many elements on the battlefield. They can create powerful black holes, levitate rocks, manipulate the gravitational force acting on enemies, and pull-down meteors—both large and small—towards the earth. The user can cause island-scale destruction by creating huge meteors and large black holes, even pulling the entire moon down to earth as an attack. Any foes will be easily destroyed by the sheer power of the user.", "fruitData": {"type": "Natural", "rarity": "Mythical", "introducedUpdate": "5", "m1Capability": true, "awakening": false, "transformation": true, "mainDescription": "Gravity allows the user to control and manipulate the gravitational force in their environment, enabling them to influence many elements on the battlefield. They can create powerful black holes, levitate rocks, manipulate the gravitational force acting on enemies, and pull-down meteors—both large and small—towards the earth. The user can cause island-scale destruction by creating huge meteors and large black holes, even pulling the entire moon down to earth as an attack. Any foes will be easily destroyed by the sheer power of the user.", "combatRating": {"pvp": "Excellent", "grinding": "Excellent"}, "furyMeterMechanics": {"gravitationalForce": {"fillMethod": "damage-based", "requiredFor": "ultimate moves", "drainTriggers": ["time", "not dealing damage"], "description": "|Upon unlocking and equipping the 3rd upgrade from the Admin Panel, Mysterious Scientist, a purple meter called \"Gravitational Force\" will be shown above the user's moveset. The meter fills up only by doing damage with Gra<PERSON>'s moves, and it very slowly depletes when the user is not dealing damage for a few seconds. The meter doesn't affect the damage or range of the moves, but it enables the use of [C] and [V] Ultimate moves when having a certain proportion of the meter filled up. On players, the bar will fill up twice as much when dealing damage to them, possibly because of allowing the user to be able to fill up the meter and use the ultimate within the duration of the fight. *If the user has only unlocked the 3rd upgrade, nothing special will happen when completely filled up. *If the user has unlocked and equipped the 4th upgrade, the bar will light on fire when completely full, similar to Kitsune and <PERSON>'s meter upon transformation. |200px|center", "requirements": {"C_ultimate": "75%", "V_ultimate": "100%"}}}, "damageResistance": null, "skinSystem": {"defaultSkin": "Green", "craftableSkins": [], "chromaticSkins": [], "acquisition": {}}, "variantsComplete": null, "variants": [], "reworkDetails": "Update 26: Gravity was remade available to purchase.. Gravity Fruit's model was slightly remade again.. * The color of the fruit model became darker.. * The pink ring now rotates around the fruit.. Gravity Fruit's icon was remade.. Added [M1] attacks.. All moves were reworked.. * [Z] Gravity Push was renamed to [Z] Singularity.. * [X] Gravity Obeisance was renamed to [X] Orbital Chain.. * [C] Meteor Pitch was renamed to [C] Gravitational Prison.. * [V] Meteors Rain was renamed to [V] Asteroid Crash.. * [F] Boulder Flight was renamed to [F] Shooting Star.. GravityIconOld.png|Previous Icon. Old Gravity.png|Previous Inventory Icon. Gravity rework hd.gif|Old Gravity Fruit's model.. GravZ.gif|Gravity Push (Old). GravX.gif|Gravity Obeisance (Old). GravC.gif|Meteor Pitch (Old). GravV.gif|Meteors Rain (Old). GravF.gif|Boulder Flight (Old) | Update 17.3: Gravity was reworked.. * Every ability got a VFX rework.. * Old: [V] Meteors Rain could not be aimed and used to hit randomly.. * New: [V] Meteors Rain can be aimed.", "economicData": {"acquisitionMethods": [{"source": "Blox Fruit Dealer"}, {"source": "Shop"}, {"source": "Sea Event"}], "reworkHistory": {}, "competitiveRanking": {"difficulty": "High", "grinding": "Excellent", "pvp": "Excellent"}, "marketAnalysis": {"pricePosition": "Cheapest Mythical fruit", "availability": "Very rare", "tradeValue": "Very High"}}, "changeHistoryAdvanced": [{"update": "26", "changes": [{"description": "Gravity was remade available to purchase.", "type": "general"}, {"description": "Gravity Fruit's model was slightly remade again.", "type": "general"}, {"description": "* The color of the fruit model became darker.", "type": "general"}, {"description": "* The pink ring now rotates around the fruit.", "type": "general"}, {"description": "Gravity Fruit's icon was remade.", "type": "general"}, {"description": "Added [M1] attacks.", "type": "addition"}, {"description": "All moves were reworked.", "type": "general"}, {"description": "* [Z] Gravity Push was renamed to [Z] Singularity.", "type": "general"}, {"description": "* [X] Gravity Obeisance was renamed to [X] Orbital Chain.", "type": "general"}, {"description": "* [C] Meteor Pitch was renamed to [C] Gravitational Prison.", "type": "general"}, {"description": "* [V] Meteors Rain was renamed to [V] Asteroid Crash.", "type": "general"}, {"description": "* [F] Boulder Flight was renamed to [F] Shooting Star.", "type": "general"}, {"description": ":", "type": "general"}], "type": "major_rework"}, {"update": "25", "changes": [{"description": "Gravity was made unavailable to purchase from the Blox Fruit Dealer's stock and Shop.", "type": "general"}], "type": "general"}, {"update": "20", "changes": [{"description": "Gravity Fruit's model was remade.", "type": "general"}], "type": "general"}, {"update": "17.3", "changes": [{"description": "Gravity was reworked.", "type": "general"}, {"description": "* Every ability got a VFX rework.", "type": "general"}, {"description": "* Old: [V] Meteors Rain could not be aimed and used to hit randomly.", "type": "general"}, {"description": "* New: [V] Meteors Rain can be aimed.", "type": "general"}], "type": "major_rework"}, {"update": "7", "changes": [{"description": "Made Gravity slightly more common.", "type": "general"}], "type": "general"}], "forms": [{"name": "Normal", "type": "Normal", "moves": [{"key": "TAP", "name": "Normal Attack", "description": "This move possesses three variations:\n• The user summons a small gravitational vortex at their cursor with a limited range, which explodes shortly after, dealing minor damage. Upgraded version has higher damage and a wider radius.\n• The user makes a swiping motion with their hand, creating a large gravitational sweep to the left, right, up and down. Upgraded version has higher damage and very high knockback.\n• The user summons a meteor from beyond the skies, landing at the location of their cursor, dealing high damage. Upgraded version summons more meteors, dealing higher damage.", "mastery": null, "gif": "https://static.wikia.nocookie.net/roblox-blox-piece/images/9/95/GravM1T.gif/revision/latest?cb=20250601223926", "gif1": "https://static.wikia.nocookie.net/roblox-blox-piece/images/c/c3/GravM1S.gif/revision/latest?cb=20250601224104", "gif2": "https://static.wikia.nocookie.net/roblox-blox-piece/images/2/2c/GravM1M.gif/revision/latest?cb=20250601224215", "damage": 2, "cooldown": 10, "energy": {"type": "unknown", "reason": "Energy cost not specified in wiki for Gravity fruit", "display": "Unknown", "note": "Gravity fruit energy costs are not documented in the official wiki"}, "furyMeter": null}, {"key": "Z", "name": "Singularity", "description": "The user creates a singularity within their hand, distorting spacetime, pulling in and stunning enemies whilst simultaneously distorting both parties' screens. The singularity then explodes in an AoE blast, knocking the enemies back.", "mastery": 1, "gif": "https://static.wikia.nocookie.net/roblox-blox-piece/images/b/b2/RGravZ.gif/revision/latest?cb=20250601225214", "gif1": null, "gif2": null, "damage": 9, "cooldown": 50, "energy": {"type": "unknown", "reason": "Energy cost not specified in wiki for Gravity fruit", "display": "Unknown", "note": "Gravity fruit energy costs are not documented in the official wiki"}, "furyMeter": null, "effects": ["stun", "aoe"]}, {"key": "X", "name": "Orbital Chain", "description": "The user creates a black hole right above them, which picks up and spews out countless debris in the direction of their cursor, dealing minor AoE damage to enemies. This move can be held down forever, however, after a few seconds the speed debris that strikes begins to slow down, causing both the damage and stun to weaken.", "mastery": 100, "gif": "https://static.wikia.nocookie.net/roblox-blox-piece/images/1/1a/RGravX.gif/revision/latest?cb=20250601225759", "gif1": null, "gif2": null, "damage": 13, "cooldown": 75, "energy": {"type": "unknown", "reason": "Energy cost not specified in wiki for Gravity fruit", "display": "Unknown", "note": "Gravity fruit energy costs are not documented in the official wiki"}, "furyMeter": null, "effects": ["stun", "aoe"]}, {"key": "C", "name": "Gravitational Prison", "description": "This move possesses two distinct variations:", "mastery": 200, "gif": "https://static.wikia.nocookie.net/roblox-blox-piece/images/8/83/GravityC2.gif/revision/latest?cb=20250601225308", "gif1": "https://static.wikia.nocookie.net/roblox-blox-piece/images/0/05/UpgradedGravityC.gif/revision/latest?cb=20250425081843", "gif2": null, "damage": 16, "cooldown": 90, "energy": {"type": "unknown", "reason": "Energy cost not specified in wiki for Gravity fruit", "display": "Unknown", "note": "Gravity fruit energy costs are not documented in the official wiki"}, "furyMeter": null}, {"key": "V", "name": "Asteroid Crash", "description": "This move possesses two distinct variations:", "mastery": 300, "gif": "https://static.wikia.nocookie.net/roblox-blox-piece/images/1/13/GravVH.gif/revision/latest?cb=20250601114738", "gif1": "https://static.wikia.nocookie.net/roblox-blox-piece/images/8/8d/GravVT.gif/revision/latest?cb=20250420041010", "gif2": null, "damage": 23, "cooldown": 100, "energy": {"type": "unknown", "reason": "Energy cost not specified in wiki for Gravity fruit", "display": "Unknown", "note": "Gravity fruit energy costs are not documented in the official wiki"}, "furyMeter": null}, {"key": "F", "name": "Shooting Star", "description": null, "mastery": 50, "gif": "https://static.wikia.nocookie.net/roblox-blox-piece/images/f/fb/GravFT.gif/revision/latest?cb=20250420041447", "gif1": "https://static.wikia.nocookie.net/roblox-blox-piece/images/9/90/GravFHG.gif/revision/latest?cb=20250601225606", "gif2": "https://static.wikia.nocookie.net/roblox-blox-piece/images/9/96/GravFHA.gif/revision/latest?cb=20250420041636", "damage": 5, "cooldown": 25, "energy": {"type": "unknown", "reason": "Energy cost not specified in wiki for Gravity fruit", "display": "Unknown", "note": "Gravity fruit energy costs are not documented in the official wiki"}, "furyMeter": null}]}], "passiveAbilities": [{"name": "Singularity Shift", "description": "Whenever the player uses Flash Step, a lingering gravitational effect appears, dealing minor damage if a target comes near the initial teleportation area. This passive is quite similar to Yeti passive but does not last as long. 200pxcenter - Gravitational Force Upon unlocking and equipping the 3rd upgrade from the Admin Panel, Mysterious Scientist, a purple meter called \"Gravitational Force\" will be shown above the user's moveset. The meter fills up only by doing damage with <PERSON><PERSON><PERSON>'s moves, and", "showcaseUrl": null}, {"name": "Gravitational Force", "description": "Upon unlocking and equipping the 3rd upgrade from the Admin Panel, Mysterious Scientist, a purple meter called \"Gravitational Force\" will be shown above the user's moveset. The meter fills up only by doing damage with Gra<PERSON>'s moves, and it very slowly depletes when the user is not dealing damage for a few seconds. The meter doesn't affect the damage or range of the moves, but it enables the use of [C] and [V] Ultimate moves when having a certain proportion of the meter filled up. On players, t", "showcaseUrl": null}, {"name": "Defensive Rift", "description": "Using the F move, \"Shooting Star\" will defend the user by making a pulse of gravity that knockbacks and damages nearby enemies whilst simultaneously backing away when they consecutively lose 20% of their HP. Using the F move multiple times before this passive takes effect will increase the amount of pulses made, increasing the damage caused by the knockback. The F move will be put on cooldown once the passive activates. 200pxcenter }", "showcaseUrl": null}], "masteryRequirements": {"Normal Attack": 0, "Singularity": 1, "Orbital Chain": 100, "Gravitational Prison": 200, "Asteroid Crash": 300, "Shooting Star": 50}, "priceData": {"current": {"money": 2500000, "status": "AVAILABLE", "robux": 2300}, "sources": ["Blox Fruit Dealer", "Shop"], "historical": [{"money": 2300, "context": "Robux price", "type": "robux"}]}, "trivia": ["Gravity is the cheapest Mythical fruit in the game.", "The second rework concept of the Gravity moves was created by concept artist @crazy_rtx.", "Gravity is the only fruit in the game to have 3 variants of its [M1] attacks.", "As of now, Gravity is the only fruit with 13 abilities, making it the fruit with the highest number of skills in the game (including [M1] clicks).", "The boss <PERSON><PERSON><PERSON> uses a slightly altered version of Gra<PERSON>, though the style of the moves resemble the old one.", "Gravity is one of the first two fruits that could obtain upgrades through the Admin Panel, the other being Eagle and soon, Creation.", "Gravity is one of six fruits to have a meter that is not intended for a transformation.", "Gravity is the only fruit in the game that requires the most tasks and items to fully upgrade it through the Admin Panel.", "Gravity is the second fruit to have a cutscene, the first being Control.", "Gravity currently has the longest cutscene of any fruit, being roughly 13.79 seconds.", "Gravity has been reworked two times and has two moves that are uncancellable.", "When going through doorways while flying, the boulder will purposely lower into the ground to allow the user to enter.", "C's upgraded version is very similar to an admin ability called \"Tensei\".", "The users are unable to use the max upgraded TAP while flying with Gravity. However, it still works even if they stop without ending the flight mode.", "Removing the stage 2 [M1] shortens the time to reach stage 3, however removing the stage 1 [M1] causes the stage 3 animation to play for a short time before becoming normal.", "A physical Gravity fruit can be dropped from defeating the Ty<PERSON> of the Skies, at a very low chance.", "Gravity and Blade are the only fruits that have had their entire moveset reworked twice.", "Gravity's [M1] has an alternative button on mobile located above [Z] Singularity that functions just the same as tapping, although resembles the activation of a move instead.", "Unlike other M1 attacks on mobile, 2 clicks are required to activate the [M1] ability."], "gallery": [{"url": "https://static.wikia.nocookie.net/roblox-blox-piece/images/1/1a/Gravity.png/revision/latest?cb=20250418030954", "caption": "Fruit Icon"}, {"url": "https://static.wikia.nocookie.net/roblox-blox-piece/images/6/6a/Gravity_rework_hd.gif/revision/latest?cb=20250120174648", "caption": "Fruit GIF"}, {"url": "https://static.wikia.nocookie.net/roblox-blox-piece/images/0/06/Gravity_150x150.png/revision/latest?cb=20231219002643", "caption": "Icon"}, {"url": "GravityWorldConceptArt.png", "caption": "Concept art of the [V] move \"The Planet\" posted on Twitter by @disardo1."}, {"url": "GravityBanner.png", "caption": "Shop artwork for the Gravity Fruit. Made by @EGOTISMS_RBLX."}, {"url": "GravityIconOld.png", "caption": "Previous Icon"}, {"url": "Old_Gravity.png", "caption": "Previous Inventory Icon"}, {"url": "Gravity_rework_hd.gif", "caption": "Old Gravity Fruit's model."}, {"url": "GravZ.gif", "caption": "Gravity Push (Old)"}, {"url": "GravX.gif", "caption": "Gravity Obeisance (Old)"}, {"url": "GravC.gif", "caption": "<PERSON><PERSON> (Old)"}, {"url": "GravV.gif", "caption": "Meteors Rain (Old)"}, {"url": "GravF.gif", "caption": "Boulder Flight (Old)"}, {"url": "Gravity_150x150.png", "caption": "Old Gravity Fruit's icon."}], "shopQuote": "Allows the user to manipulate gravity, controlling the battlefield.", "upgradeData": {}, "instinctData": {"Tap": {"name": "Normal Attack", "canDodge": true, "breaksInstinct": false, "notes": ""}, "Z": {"name": "Singularity", "canDodge": false, "breaksInstinct": true, "notes": "Only the pull when held can be dodged. The explosion breaks Instinct."}, "X": {"name": "Orbital Chain", "canDodge": false, "breaksInstinct": true, "notes": "Breaks Instinct no matter what."}, "C": {"name": "Gravitational Prison (Held)", "canDodge": false, "breaksInstinct": true, "notes": "Breaks Instinct no matter what."}, "V": {"name": "Asteroid Crash (Held)", "canDodge": false, "breaksInstinct": true, "notes": "Breaks Instinct no matter what."}, "F": {"name": "Shooting Star", "canDodge": false, "breaksInstinct": true, "notes": "Breaks Instinct no matter what."}}, "pros": ["Insane damage.", "High trade value.", "Great combo potential.", "All moves have a huge hitbox.", "All moves break Instinct.", "Can be good for grinding"], "cons": ["Weak in air.", "Cannot catch up with fast fruits/swords users. (for example: Light, Portal, etc.)", "Moves have relatively longer start-up than other PvP fruits, making it easier for enemies to dodge the attacks.", "Requires a certain amount of prediction.", "Passive teleportation completely overrides Draco and Human V4 flash step.", "Has some end-lag.", "High mastery requirement. (Even higher to unlock the upgrades)"], "formSpecific": {"General": {"pros": ["Insane damage.", "High trade value.", "Great combo potential.", "All moves have a huge hitbox.", "All moves break Instinct.", "Can be good for grinding"], "cons": ["Weak in air.", "Cannot catch up with fast fruits/swords users. (for example: Light, Portal, etc.)", "Moves have relatively longer start-up than other PvP fruits, making it easier for enemies to dodge the attacks.", "Requires a certain amount of prediction.", "Passive teleportation completely overrides Draco and Human V4 flash step.", "Has some end-lag.", "High mastery requirement. (Even higher to unlock the upgrades)"]}}}, "imageUrl": "https://static.wikia.nocookie.net/roblox-blox-piece/images/5/5f/Gravity_Fruit.png/revision/latest?cb=20250418030958", "imageUrls": ["https://static.wikia.nocookie.net/roblox-blox-piece/images/b/b5/GravitationalStepsofForce.gif/revision/latest?cb=20250418174108", "https://static.wikia.nocookie.net/roblox-blox-piece/images/a/ae/GravitationalForce.png/revision/latest?cb=20250419082355", "https://static.wikia.nocookie.net/roblox-blox-piece/images/9/95/GravM1T.gif/revision/latest?cb=20250601223926", "https://static.wikia.nocookie.net/roblox-blox-piece/images/b/b2/RGravZ.gif/revision/latest?cb=20250601225214", "https://static.wikia.nocookie.net/roblox-blox-piece/images/e/ec/GravityDefense.gif/revision/latest?cb=20250425060333", "https://static.wikia.nocookie.net/roblox-blox-piece/images/5/5f/Gravity_Fruit.png/revision/latest?cb=20250418030958", "https://static.wikia.nocookie.net/roblox-blox-piece/images/8/83/GravityC2.gif/revision/latest?cb=20250601225308", "https://static.wikia.nocookie.net/roblox-blox-piece/images/1/1a/RGravX.gif/revision/latest?cb=20250601225759", "https://static.wikia.nocookie.net/roblox-blox-piece/images/8/8d/GravVT.gif/revision/latest?cb=20250420041010", "https://static.wikia.nocookie.net/roblox-blox-piece/images/f/fb/GravFT.gif/revision/latest?cb=20250420041447", "https://static.wikia.nocookie.net/roblox-blox-piece/images/1/1a/Gravity.png/revision/latest?cb=20250418030954", "https://static.wikia.nocookie.net/roblox-blox-piece/images/3/31/GravityFruitReworkedGif.gif/revision/latest?cb=20250418132752", "https://static.wikia.nocookie.net/roblox-blox-piece/images/9/92/GravityWorldConceptArt.png/revision/latest?cb=20250426041021", "https://static.wikia.nocookie.net/roblox-blox-piece/images/7/7c/GravityBanner.png/revision/latest?cb=20250516093105", "https://static.wikia.nocookie.net/roblox-blox-piece/images/6/6d/GravityIconOld.png/revision/latest?cb=20250523083109", "https://static.wikia.nocookie.net/roblox-blox-piece/images/9/91/GravZ.gif/revision/latest?cb=20230620061732", "https://static.wikia.nocookie.net/roblox-blox-piece/images/7/7e/GravX.gif/revision/latest?cb=20230620061907", "https://static.wikia.nocookie.net/roblox-blox-piece/images/b/be/GravC.gif/revision/latest?cb=20230620061959", "https://static.wikia.nocookie.net/roblox-blox-piece/images/6/6a/Gravity_rework_hd.gif/revision/latest?cb=20250120174648", "https://static.wikia.nocookie.net/roblox-blox-piece/images/3/31/Old_Gravity.png/revision/latest?cb=20250420045334", "https://static.wikia.nocookie.net/roblox-blox-piece/images/0/06/Gravity_150x150.png/revision/latest?cb=20231219002643", "https://static.wikia.nocookie.net/roblox-blox-piece/images/5/54/GravF.gif/revision/latest?cb=20230620062051", "https://static.wikia.nocookie.net/roblox-blox-piece/images/2/2c/GravV.gif/revision/latest?cb=20230620150556"], "lastUpdated": {"$date": "2025-07-17T19:20:43.796Z"}, "rawData": {"infobox": {"name": "Gravity", "image": "Gravity Fruit.png|Fruit Icon GravityFruitReworkedGif.gif|Fruit GIF Gravity.png|Icon", "type": "Natural", "rarity": "Mythical", "m1": "Yes", "update": "5", "money": "2,500,000"}, "wikitextLength": 23756, "movesFound": 6, "statsFound": 6, "extractedAt": "2025-07-17T19:20:43.796Z"}, "type": "fruit", "wikiUrl": "https://blox-fruits.fandom.com/wiki/Gravity"}