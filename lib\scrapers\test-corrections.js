const fs = require('fs');

// Test script to validate our corrections
async function testCorrections() {
  console.log('🧪 Testing scraper corrections...');
  
  // Read the scraped data files
  const dragonData = fs.readFileSync('Scraped.txt', 'utf8');
  const gravityData = fs.readFileSync('Scraped2.txt', 'utf8');
  
  console.log('\n📊 Analyzing Dragon data...');
  
  // Parse Dragon data
  let dragon;
  try {
    dragon = JSON.parse(dragonData);
  } catch (e) {
    console.error('❌ Failed to parse Dragon data:', e.message);
    return;
  }
  
  // Check for issues in Dragon data
  console.log('\n🔍 Dragon Issues Analysis:');
  
  // Check for "Unknown" damage values
  let unknownDamageCount = 0;
  if (dragon.fruitData && dragon.fruitData.forms) {
    dragon.fruitData.forms.forEach((form, formIndex) => {
      if (form.moves) {
        form.moves.forEach((move, moveIndex) => {
          if (move.damage && (move.damage === "Unknown" || (typeof move.damage === 'object' && move.damage.display === "Unknown"))) {
            unknownDamageCount++;
            console.log(`   ⚠️ Form ${formIndex} Move ${moveIndex} (${move.name}): damage = ${JSON.stringify(move.damage)}`);
          }
        });
      }
    });
  }
  
  // Check pros/cons classification
  let misclassifiedPros = 0;
  if (dragon.pros) {
    dragon.pros.forEach(pro => {
      if (pro.toLowerCase().includes('hard to control') || 
          pro.toLowerCase().includes('difficult to control') ||
          pro.toLowerCase().includes('vulnerable')) {
        misclassifiedPros++;
        console.log(`   ⚠️ Misclassified pro: "${pro}"`);
      }
    });
  }
  
  console.log(`\n📈 Dragon Summary:`);
  console.log(`   - Unknown damage values: ${unknownDamageCount}`);
  console.log(`   - Misclassified pros: ${misclassifiedPros}`);
  
  console.log('\n📊 Analyzing Gravity data...');
  
  // Parse Gravity data
  let gravity;
  try {
    gravity = JSON.parse(gravityData);
  } catch (e) {
    console.error('❌ Failed to parse Gravity data:', e.message);
    return;
  }
  
  // Check for issues in Gravity data
  console.log('\n🔍 Gravity Issues Analysis:');
  
  // Check damage resistance
  const damageResistance = gravity.fruitData?.damageResistance;
  console.log(`   - Damage resistance: ${JSON.stringify(damageResistance)}`);
  
  // Check energy values
  let unknownEnergyCount = 0;
  if (gravity.fruitData && gravity.fruitData.forms) {
    gravity.fruitData.forms.forEach((form, formIndex) => {
      if (form.moves) {
        form.moves.forEach((move, moveIndex) => {
          if (move.energy && (move.energy === "Unknown" || (typeof move.energy === 'object' && move.energy.display === "Unknown"))) {
            unknownEnergyCount++;
            console.log(`   ⚠️ Form ${formIndex} Move ${moveIndex} (${move.name}): energy = ${JSON.stringify(move.energy)}`);
          }
        });
      }
    });
  }
  
  // Check passive abilities descriptions
  let truncatedPassives = 0;
  if (gravity.fruitData && gravity.fruitData.passiveAbilities) {
    gravity.fruitData.passiveAbilities.forEach(passive => {
      if (passive.description && passive.description.length < 20) {
        truncatedPassives++;
        console.log(`   ⚠️ Truncated passive: "${passive.name}" - "${passive.description}"`);
      }
    });
  }
  
  // Check upgrade data
  const upgradeData = gravity.fruitData?.upgradeData;
  const upgradeDataEmpty = !upgradeData || Object.keys(upgradeData).length === 0;
  console.log(`   - Upgrade data empty: ${upgradeDataEmpty}`);
  
  console.log(`\n📈 Gravity Summary:`);
  console.log(`   - Unknown energy values: ${unknownEnergyCount}`);
  console.log(`   - Truncated passives: ${truncatedPassives}`);
  console.log(`   - Upgrade data empty: ${upgradeDataEmpty}`);
  console.log(`   - Damage resistance null: ${damageResistance === null}`);
  
  console.log('\n✅ Analysis complete!');
}

testCorrections().catch(console.error);
