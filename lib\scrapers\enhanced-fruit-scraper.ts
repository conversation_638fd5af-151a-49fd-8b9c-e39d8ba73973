/**
 * Enhanced Fruit Scraper with improved data extraction capabilities
 * Handles complex wikitext patterns and missing data recovery
 */

import { BaseScraper } from "./base-scraper"
import { ScrapedItem, FruitData } from "./types"

export class EnhancedFruitScraper extends BaseScraper {
  /**
   * Scrape all fruits from a given category (default: "Blox_Fruits")
   */
  async scrapeCategory(categoryName: string = "Blox_Fruits"): Promise<ScrapedItem[]> {
    console.log(`\n🎯 Starting to scrape fruits from category: ${categoryName}`);
    // Get all page titles in the category using a helper (assume getCategoryPageTitles exists in BaseScraper)
    const titles = await this.getCategoryPageTitles(categoryName);
    const results: ScrapedItem[] = [];
    for (const title of titles) {
      const item = await this.scrapeItem(title, "fruit");
      if (item) results.push(item);
    }
    return results;
  }

  async scrapeItem(title: string, itemType: string = "fruit"): Promise<ScrapedItem | null> {
    console.log(`🍎 Scraping enhanced fruit data for: ${title}`)
    
    const wikitext = await this.getPageContent(title)
    if (!wikitext) {
      console.error(`❌ No content found for ${title}`)
      return null
    }

    const infoboxData = this.extractTemplateData(wikitext, "Blox Fruit Infobox")
    let fruitData = this.extractEnhancedFruitData(wikitext, infoboxData)

    // Collect all image names from wikitext and infobox for main images
    const imageNames: string[] = []

    // 1. From infobox (gallery, image, icon, etc.)
    const infoboxImageFields = [
      "image", "Image", "icon", "Icon", "image1", "image2", "image3"
    ]
    for (const key of infoboxImageFields) {
      const val = infoboxData[key]
      if (val && typeof val === "string") {
        // Gallery
        const galleryMatch = val.match(/<gallery>([\s\S]*?)<\/gallery>/i)
        if (galleryMatch) {
          const galleryContent = galleryMatch[1]
          galleryContent.split('\n').forEach(line => {
            const img = line.split('|')[0].trim()
            if (img && img.match(/\.(png|jpg|jpeg|gif|webp)$/i)) imageNames.push(img)
          })
        } else if (val.match(/\.(png|jpg|jpeg|gif|webp)$/i)) {
          imageNames.push(val.trim())
        }
      }
    }

    // 2. From [[File:...]] in wikitext
    const filePattern = /\[\[File:([^|\]]+\.(?:png|jpg|jpeg|gif|webp))/gi
    let match
    while ((match = filePattern.exec(wikitext)) !== null) {
      const img = match[1].trim()
      if (img && !imageNames.includes(img)) imageNames.push(img)
    }

    // 3. From GIFs in SkillBox
    const gifPattern = /\|GIF\s*=\s*([^\n|]+)/g
    while ((match = gifPattern.exec(wikitext)) !== null) {
      const gifName = match[1].trim()
      if (gifName && !gifName.includes('|')) {
        const gifFile = gifName.match(/\.(gif|png|jpg|jpeg|webp)$/i) ? gifName : gifName + '.gif'
        if (!imageNames.includes(gifFile)) imageNames.push(gifFile)
      }
    }

    // 4. From gallery tags in wikitext
    const galleryPattern = /<gallery>([\s\S]*?)<\/gallery>/g
    while ((match = galleryPattern.exec(wikitext)) !== null) {
      const galleryContent = match[1]
      const itemPattern = /([^|\n]+\.(?:png|jpg|jpeg|gif|webp))(?:\|[^\n]*)?/g
      let itemMatch
      while ((itemMatch = itemPattern.exec(galleryContent)) !== null) {
        const img = itemMatch[1].trim()
        if (img && !imageNames.includes(img)) imageNames.push(img)
      }
    }

    // Remove duplicates
    const uniqueImageNames = Array.from(new Set(imageNames))

    // --- GIF URL PATCH: collect all gif/gif1/gif2 from all moves ---
    const gifNames: string[] = []
    const gifToMoveMapping: Map<string, { formName: string; moveName: string; moveKey: string }> = new Map()
    
    if (fruitData && fruitData.forms && Array.isArray(fruitData.forms)) {
      for (const form of fruitData.forms) {
        if (form.moves && Array.isArray(form.moves)) {
          for (const move of form.moves) {
            const formName = form.name || 'Unknown';
            const moveName = move.name || 'Unknown';
            const moveKey = move.key || 'Unknown';
            
            // Process gif, gif1, gif2 and create mapping
            const gifFields: (keyof typeof move)[] = ['gif', 'gif1', 'gif2'];
            for (const gifField of gifFields) {
              if (move[gifField] && typeof move[gifField] === "string") {
                let fname = move[gifField] as string
                
                // Extract filename from URL if it's already a URL (from wrong domain or any domain)
                if (fname.startsWith("http")) {
                  const urlParts = fname.split("/");
                  fname = urlParts[urlParts.length - 1].split("?")[0];
                }
                if (!fname.match(/\.(gif|png|jpg|jpeg|webp)$/i)) fname += ".gif"
                
                if (!gifNames.includes(fname)) {
                  gifNames.push(fname);
                  // Create mapping for validation
                  gifToMoveMapping.set(fname, { formName, moveName, moveKey });
                  console.log(`🔗 GIF mapping: ${fname} -> ${formName}/${moveName} (${moveKey})`);
                }
              }
            }
          }
        }
      }
    }

    // Resolve all image URLs using the API
    const imageUrls = await this.getImageUrls(uniqueImageNames)
    // Resolve all gif URLs using the API
    const gifUrlsArr = gifNames.length > 0 ? await this.getImageUrls(gifNames) : []
    // Map: filename (lowercase) => url
    const gifUrlMap = new Map<string, string>()
    gifNames.forEach((name, i) => {
      if (gifUrlsArr[i]) gifUrlMap.set(name.toLowerCase(), gifUrlsArr[i])
    })

    // Patch fruitData.forms moves gif/gif1/gif2 to be URLs with validation
    if (fruitData && fruitData.forms && Array.isArray(fruitData.forms)) {
      for (const form of fruitData.forms) {
        if (form.moves && Array.isArray(form.moves)) {
          for (const move of form.moves) {
            const formName = form.name || 'Unknown';
            const moveName = move.name || 'Unknown';
            const moveKey = move.key || 'Unknown';
            
            // Process gif, gif1, gif2 with validation
            const gifFields: (keyof typeof move)[] = ['gif', 'gif1', 'gif2'];
            for (const gifField of gifFields) {
              if (move[gifField] && typeof move[gifField] === "string") {
                let fname = move[gifField] as string
                
                // Extract filename from URL if it's already a URL (from wrong domain or any domain)
                if (fname.startsWith("http")) {
                  const urlParts = fname.split("/");
                  fname = urlParts[urlParts.length - 1].split("?")[0];
                }
                if (!fname.match(/\.(gif|png|jpg|jpeg|webp)$/i)) fname += ".gif"
                
                const url = gifUrlMap.get(fname.toLowerCase())
                if (url) {
                  (move as any)[gifField] = url;
                  console.log(`✅ GIF resolved: ${fname} -> ${formName}/${moveName} (${moveKey})`);
                } else {
                  console.log(`❌ GIF not found: ${fname} for ${formName}/${moveName} (${moveKey})`);
                  (move as any)[gifField] = undefined;
                }
              }
            }
          }
        }
      }
    }
    
    // Patch fruitData.gallery[*].url to be resolved URLs (robust normalization)
    if (fruitData && Array.isArray(fruitData.gallery)) {
      // 1. Collect all normalized filenames from gallery
      const galleryFilenamesSet = new Set<string>();
      for (const item of fruitData.gallery) {
        if (item.url && typeof item.url === "string") {
          // Always extract filename from url (even if it's a full URL)
          let fname = item.url.trim();
          // If it's a URL, extract the filename
          if (fname.startsWith("http")) {
            const urlParts = fname.split("/");
            fname = urlParts[urlParts.length - 1].split("?")[0];
          }
          // Normalize: lowercase, underscores, add .png if missing extension
          fname = fname.replace(/ /g, "_").toLowerCase();
          if (!fname.match(/\.(gif|png|jpg|jpeg|webp)$/i)) fname += ".png";
          galleryFilenamesSet.add(fname);
        }
      }
      const uniqueGalleryFilenames = Array.from(galleryFilenamesSet);
      // 2. Resolve all filenames via getImageUrls
      if (uniqueGalleryFilenames.length > 0) {
        const galleryUrlsArr = await this.getImageUrls(uniqueGalleryFilenames);
        // 3. Build map: normalized filename -> resolved URL
        const galleryUrlMap = new Map<string, string>();
        uniqueGalleryFilenames.forEach((name, i) => {
          if (galleryUrlsArr[i]) galleryUrlMap.set(name, galleryUrlsArr[i]);
        });
        // 4. Patch gallery: replace .url with resolved URL if found
        for (const item of fruitData.gallery) {
          if (item.url && typeof item.url === "string") {
            const originalUrl = item.url.trim();
            let fname = originalUrl;
            
            // Extract filename from URL if it's already a URL
            if (fname.startsWith("http")) {
              const urlParts = fname.split("/");
              fname = urlParts[urlParts.length - 1].split("?")[0];
            }
            
            // Normalize for API lookup
            fname = fname.replace(/ /g, "_").toLowerCase();
            if (!fname.match(/\.(gif|png|jpg|jpeg|webp)$/i)) fname += ".png";
            
            const resolvedUrl = galleryUrlMap.get(fname);
            if (resolvedUrl) {
              item.url = resolvedUrl;
            } else {
              // Keep only the filename, not any URL or File: prefix
              console.warn(`[Gallery Image Unresolved] "${originalUrl}" not found in Fandom API results. Keeping filename only.`);
              
              // Extract clean filename from original
              let cleanFilename = originalUrl.replace(/^File:/i, '').trim();
              
              // If it was already a URL, extract just the filename
              if (cleanFilename.startsWith("http")) {
                const urlParts = cleanFilename.split("/");
                cleanFilename = urlParts[urlParts.length - 1].split("?")[0];
              }
              
              item.url = cleanFilename;
            }
          }
        }
      }
    }

    // --- PATCH: Remove images section from fruitData.forms ---
    if (fruitData && Array.isArray(fruitData.forms)) {
      for (const form of fruitData.forms) {
        if (form.images) {
          console.log(`🗑️ Removing images section from form`);
          delete form.images;
        }
      }
    }

    // Prioritize imageUrl: look for a main icon/fruit image
    let imageUrl: string | undefined = undefined
    // 1. Try to find a gallery image with caption "Icon" or "Fruit Icon"
    let galleryIcon: string | undefined = undefined
    try {
      const gallery = this.extractGalleryData(wikitext)
      const iconEntry = gallery.find(
        g => g.caption.toLowerCase().includes("icon") || g.caption.toLowerCase().includes("fruit icon")
      )
      if (iconEntry) {
        // Find the matching URL in imageUrls (by filename)
        const filename = iconEntry.url.split('/').pop()?.split('?')[0]?.toLowerCase()
        const matchUrl = imageUrls.find(url => url.toLowerCase().includes(filename || ""))
        if (matchUrl) galleryIcon = matchUrl
        else galleryIcon = iconEntry.url
      }
    } catch {}

    // 2. Try to find a file named "Dragon.png" or "Dragon_Fruit.png" or similar
    let mainFile: string | undefined = undefined
    const mainNames = ["dragon.png", "dragon_fruit.png", "dragon fruit.png"]
    for (const url of imageUrls) {
      const fname = url.split('/').pop()?.split('?')[0]?.toLowerCase()
      if (fname && mainNames.includes(fname)) {
        mainFile = url
        break
      }
    }

    // 3. Fallback: first image in imageUrls
    imageUrl = galleryIcon || mainFile || (imageUrls.length > 0 ? imageUrls[0] : undefined)

    const scrapedItem: ScrapedItem = {
      name: title,
      type: "fruit",
      category: "fruit",
      description: fruitData.mainDescription,
      imageUrl,
      imageUrls,
      wikiUrl: `https://blox-fruits.fandom.com/wiki/${encodeURIComponent(title)}`,
      lastUpdated: new Date(),
      fruitData,
      rawData: {
        infobox: infoboxData,
        wikitextLength: wikitext.length,
        movesFound: this.countMoves(wikitext),
        statsFound: this.countStats(wikitext),
        extractedAt: new Date().toISOString()
      }
    }

    return scrapedItem
  }

  extractSpecificData(wikitext: string, infoboxData: Record<string, string>): FruitData {
    return this.extractEnhancedFruitData(wikitext, infoboxData)
  }

  private extractEnhancedFruitData(wikitext: string, infoboxData: Record<string, string>): FruitData {
    console.log("🔍 Extracting enhanced fruit data...")
    
    // Base fruit data from infobox
    const baseFruitData = this.extractBaseFruitData(wikitext, infoboxData)
    
    // Enhanced data extraction using improved stats extractor
    const statsTableData = this.extractStatsTableData(wikitext);
    const enhancedData = this.extractEnhancedStatsDataWithFruitLogic(wikitext)
    
    console.log("📊 Enhanced data extracted:");
    console.log(`  - Stats data forms: ${Object.keys(enhancedData.statsData || {}).length}`);
    console.log(`  - Skill data forms: ${Object.keys(enhancedData.skillData || {}).length}`);
    console.log(`  - Overview data forms: ${Object.keys(enhancedData.overviewData || {}).length}`);
    console.log(`  - Passive abilities: ${enhancedData.passiveAbilities?.length || 0}`);

    // Specific fruit mechanics - fix Gravity contradictions
    let furyMeterMechanics = this.extractFuryMeterMechanics(wikitext)
    
    // Use enhanced passive abilities if available, otherwise fallback to standard extraction
    const passiveAbilities = enhancedData.passiveAbilities && enhancedData.passiveAbilities.length > 0
      ? enhancedData.passiveAbilities
      : this.extractPassiveAbilities(wikitext)
    
    // Fix Gravity fruit contradictions: if passiveAbilities mentions "Gravitational Force" but furyMeterMechanics is null
    if (!furyMeterMechanics && passiveAbilities.some((p: any) => p.name.includes('Gravitational Force'))) {
      console.log('⚠️ Fixing Gravity contradiction: passiveAbilities mentions Gravitational Force but furyMeterMechanics is null');
      furyMeterMechanics = {
        gravitationalForce: {
          fillMethod: "damage-based",
          requiredFor: "ultimate moves",
          drainTriggers: ["time", "not dealing damage"],
          description: "The Gravity fruit uses a Gravitational Force meter that fills through dealing damage. When filled to 75%, it enables the ultimate C move, and when filled to 100%, it enables the ultimate V move.",
          requirements: {
            "C_ultimate": "75%",
            "V_ultimate": "100%"
          }
        }
      };
    }
    const masteryRequirements = this.extractMasteryRequirements(wikitext)

    // Price and economic data
    const priceData = this.extractPriceData(wikitext, infoboxData)
    const economicData = this.extractEconomicData(wikitext)

    // Transformation and variant data - use enhanced stats data as primary source
    const primaryStatsData = Object.keys(enhancedData.statsData || {}).length > 0 
      ? enhancedData.statsData 
      : statsTableData;
    
    const forms = this.extractFormsData(enhancedData.skillData || {}, primaryStatsData || {})
    const variantsComplete = enhancedData.variantData
    const variants = this.extractVariants(wikitext);

    // Additional data
    const trivia = this.extractTrivia(wikitext)
    const gallery = this.extractGalleryData(wikitext)
    const changeHistory = this.extractChangeHistory(wikitext)
    const skinSystem = this.extractSkinSystem(wikitext)
    const reworkDetails = this.extractReworkDetails(wikitext);

    // Shop quote
    const shopQuote = this.extractShopQuote(wikitext)

    // Fruit-specific data (Gravity upgrades, instinct data, etc.)
    const fruitSpecificData: any = {};
    if (enhancedData.upgradeData) {
      fruitSpecificData.upgradeData = enhancedData.upgradeData;
    }
    if (enhancedData.instinctData) {
      fruitSpecificData.instinctData = enhancedData.instinctData;
    }

    const overview = this.processOverviewData(enhancedData.overviewData);

    return {
      ...baseFruitData,
      furyMeterMechanics,
      damageResistance: enhancedData.damageResistance,
      skinSystem,
      variantsComplete: enhancedData.variantData,
      variants,
      reworkDetails,
      economicData,
      changeHistoryAdvanced: changeHistory,
      forms,
      passiveAbilities,
      masteryRequirements,
      priceData,
      trivia,
      gallery,
      shopQuote,
      ...fruitSpecificData,
      // Consolidate pros and cons into a single structure
      pros: overview.pros,
      cons: overview.cons,
      formSpecific: overview.formSpecific
    }
  }

  private extractStatsTableData(wikitext: string): Record<string, any[]> {
    const statsData: Record<string, any[]> = {};
    const tablePattern = /\{\{Stats Table\|Name=([^|]+)[\s\S]*?([^}]+)\}\}/g;
    let match;

    while ((match = tablePattern.exec(wikitext)) !== null) {
      const formName = match[1].trim();
      const tableContent = match[2];
      statsData[formName] = this.parseStatsTable(tableContent);
    }

    return statsData;
  }

  private parseStatsTable(tableContent: string): any[] {
    const moves: any[] = [];
    const rowPattern = /\{\{Stats Table Row\|([^}]+)\}\}/g;
    let rowMatch;

    while ((rowMatch = rowPattern.exec(tableContent)) !== null) {
      const rowData = this.parseTableRow(rowMatch[1]);
      if (rowData) {
        moves.push(rowData);
      }
    }

    return moves;
  }

  private parseTableRow(rowData: string): any | null {
    const data: any = {};
    const parts = rowData.split("|");
    
    for (const part of parts) {
      const [key, value] = part.split("=").map(s => s.trim());
      if (key && value) {
        data[key.toLowerCase()] = this.cleanWikitext(value);
      }
    }

    return Object.keys(data).length > 0 ? data : null;
  }

  private extractReworkDetails(wikitext: string): string {
    // Look for Change History section first
    const changeHistoryMatch = wikitext.match(/==Change History==([\s\S]*?)(?===|$)/);
    if (changeHistoryMatch) {
      const historyContent = changeHistoryMatch[1];

      // Extract major rework information
      const reworkInfo: string[] = [];

      // Look for update sections with rework mentions
      const updatePattern = /\{\{Update\|([^}]+)\}\}([\s\S]*?)(?=\{\{Update|\-\-\-\-|$)/g;
      let match;

      while ((match = updatePattern.exec(historyContent)) !== null) {
        const updateNumber = match[1];
        const updateContent = match[2];

        if (updateContent.toLowerCase().includes('rework')) {
          const reworkLines = updateContent.split('\n')
            .filter(line => line.trim() && !line.includes('gallery'))
            .map(line => this.cleanWikitext(line.replace(/^\*\s*/, '')))
            .filter(line => line.length > 5);

          if (reworkLines.length > 0) {
            reworkInfo.push(`Update ${updateNumber}: ${reworkLines.join('. ')}`);
          }
        }
      }

      if (reworkInfo.length > 0) {
        return reworkInfo.join(' | ');
      }
    }

    // Fallback patterns for specific cases
    const reworkPatterns = [
      // Specific Dragon Token pattern
      /Dragon Token Upon the release of Update 24[\s\S]*?The old version of Dragon was made unobtainable\./,
      // General rework patterns
      /rework[\s\S]*?Update \d+[\s\S]*?(?=\n\n|==)/i,
      // Token system patterns
      /Token[\s\S]*?conversion[\s\S]*?(?=\n\n|==)/i,
      // Major changes patterns
      /major changes[\s\S]*?update[\s\S]*?(?=\n\n|==)/i
    ]

    for (const pattern of reworkPatterns) {
      const match = wikitext.match(pattern)
      if (match && match[0].length > 20) {
        return this.cleanWikitext(match[0])
      }
    }

    return ""
  }

  private extractVariants(wikitext: string): any[] {
    const variants = [];
    
    // Check for Dragon variants (Eastern/Western)
    if (wikitext.includes("Eastern Dragon") && wikitext.includes("Western Dragon")) {
      const mountingMatch = wikitext.match(/Maximum is (\d+) for Eastern Form and (\d+) for Western Form/);
      if (mountingMatch) {
        variants.push({
          name: "Eastern Dragon",
          type: "East",
          mountingCapacity: parseInt(mountingMatch[1]),
          culturalTheme: "East Asian culture",
          characteristics: ["spinning movements", "upward spirals", "smaller hitbox"]
        });
        variants.push({
          name: "Western Dragon", 
          type: "West",
          mountingCapacity: parseInt(mountingMatch[2]),
          culturalTheme: "European folklore",
          characteristics: ["claw attacks", "fastest flight speed", "large hitbox", "long range"]
        });
      }
    }
    
    // Legacy pattern for other potential variant formats
    const variantPattern = /\{\{Variant\|Name=([^|]+)\|FlightSpeed=([^|]+)\|MountingCapacity=(\d+)\|DamageResistance=([^|]+)\|SpecialFeatures=([^}]+)\}\}/g;
    let match;

    while ((match = variantPattern.exec(wikitext)) !== null) {
      variants.push({
        name: match[1],
        flightSpeed: this.extractFlightSpeed(wikitext, match[1]),
        mountingCapacity: parseInt(match[3]),
        damageResistance: match[4],
        specialFeatures: match[5].split(", ")
      });
    }

    return variants;
  }

  private extractFlightSpeed(wikitext: string, variantName: string): string | null {
    const flightSpeedPattern = new RegExp(`${variantName}.*propelling them forwards faster than their original flying speed`);
    const match = wikitext.match(flightSpeedPattern);
    if (match) {
      return "Faster than original flying speed";
    }
    return null;
  }

  private extractBaseFruitData(wikitext: string, infoboxData: Record<string, string>): Partial<FruitData> {
    return {
      type: infoboxData.type as any,
      rarity: infoboxData.rarity as any,
      introducedUpdate: infoboxData.update,
      m1Capability: infoboxData.m1?.toLowerCase() === 'yes',
      awakening: false, // Most fruits don't have awakening
      transformation: this.hasTransformation(wikitext),
      mainDescription: this.extractMainDescription(wikitext),
      combatRating: this.extractCombatRating(wikitext)
    }
  }

  private extractFuryMeterMechanics(wikitext: string): any {
    console.log('🔍 Enhanced Fruit Scraper: Extracting Fury Meter mechanics...');

    // Pour Dragon, utiliser la méthode spécialisée
    if (wikitext.toLowerCase().includes('dragon') && wikitext.includes('Fury Meter')) {
      console.log('🐉 Detected Dragon fruit, using specialized extraction');
      return this.extractDragonFuryMechanics(wikitext);
    }

    // Pour Gravity, extraire Gravitational Force
    if (wikitext.toLowerCase().includes('gravity') && wikitext.includes('Gravitational Force')) {
      console.log('🌌 Detected Gravity fruit, extracting Gravitational Force');
      return this.extractGravityMechanics(wikitext);
    }

    // Pour autres fruits avec Fury Meter
    if (wikitext.includes('Fury Meter')) {
      console.log('⚡ Generic Fury Meter detected');
      return {
        description: "Fruit uses a Fury Meter system",
        furyMeter: {
          fillMethod: "time-based",
          requiredFor: "transformation",
          drainTriggers: ["move usage"]
        }
      };
    }

    console.log('❌ No special meter mechanics found');
    return null;
  }

  private extractDragonFuryMechanics(wikitext: string): any {
    console.log('🐉 Extracting Dragon-specific Fury Meter mechanics');

    // Chercher la section Fury Meter proprement dans les passives
    const furyMeterSection = wikitext.match(/\|\s*'''?\[\[Fury Meter\]\]'''?\s*\n\*\s*(.*?)(?=\n\|-|\n\|[^*])/is);

    if (furyMeterSection) {
      const content = furyMeterSection[1];
      const cleanContent = content.replace(/\[\[[^\]]+\]\]/g, '').replace(/\{\{[^}]+\}\}/g, '');

      console.log(`✅ Found Dragon Fury Meter section (${cleanContent.length} chars)`);

      return {
        description: "Dragon fruit uses a dual Fury Meter system for transformations",
        orangeMeter: {
          fillMethod: "time-based",
          requiredFor: "hybrid transformation",
          drainTriggers: ["move usage", "taking damage"],
          description: "The Orange meter refills slowly over time when not transformed and drains with any move used"
        },
        redMeter: {
          fillMethod: "damage-based",
          requiredFor: "full transformation",
          drainTriggers: ["taking damage"],
          description: "The second Fury Meter fills only by dealing damage and must be full to transform into full Dragon Form"
        },
        transformationMechanics: {
          passiveDrain: false,
          allowsIndefiniteTransformation: true,
          hybridRequirement: "Orange meter filled",
          fullTransformationRequirement: "Both meters filled",
          drainBehavior: "While transformed, the Fury meter begins draining for a short time, indicated by flames above it, and taking damage starts draining it again. However, the Fury meter doesn't drain passively while transformed, allowing indefinite transformation as long as it is not emptied."
        }
      };
    }

    console.log('⚠️ Dragon Fury Meter section not found, using defaults');
    return {
      description: "Dragon fruit uses a dual Fury Meter system for transformations",
      orangeMeter: {
        fillMethod: "time-based",
        requiredFor: "hybrid transformation",
        drainTriggers: ["move usage", "taking damage"],
        description: "First meter for hybrid transformation"
      },
      redMeter: {
        fillMethod: "damage-based",
        requiredFor: "full transformation",
        drainTriggers: ["taking damage"],
        description: "Second meter for full transformation"
      }
    };
  }

  private extractGravityMechanics(wikitext: string): any {
    console.log('🌌 Extracting Gravity-specific mechanics');

    // More robust pattern for Gravitational Force description
    const gravityMeterMatch = wikitext.match(/Gravitational Force[^|]*?\|([\s\S]*?)(?=\|-|\|})/);
    if (gravityMeterMatch) {
      const description = this.cleanWikitext(gravityMeterMatch[1]);
      // Vérifier que la description n'est pas une suite de chiffres
      if (description.match(/^\d+(,?\d+)*\s+\d+\s+\d+\s+\d+$/)) {
        console.log('⚠️ Invalid Gravitational Force description detected (numbers only), trying alternative extraction');
        return this.extractGravityMechanicsAlternative(wikitext);
      }
      return {
        gravitationalForce: {
          fillMethod: "damage-based",
          requiredFor: "ultimate moves",
          drainTriggers: ["time", "not dealing damage"],
          description: description,
          requirements: {
            "C_ultimate": "75%",
            "V_ultimate": "100%"
          }
        }
      };
    }

    return this.extractGravityMechanicsAlternative(wikitext);
  }

  /**
   * Alternative extraction method for Gravity mechanics
   */
  private extractGravityMechanicsAlternative(wikitext: string): any {
    console.log('🌌 Attempting alternative Gravity mechanics extraction');
    
    // Look for Gravitational Force mentioned in passive abilities
    const gravityPassivePattern = /'''Gravitational Force'''[\s\S]*?([\s\S]*?)(?=\|-|\|}|''')/;
    const gravityPassiveMatch = wikitext.match(gravityPassivePattern);
    
    if (gravityPassiveMatch) {
      const description = this.cleanWikitext(gravityPassiveMatch[1]);
      if (description && description.length > 10) {
        return {
          gravitationalForce: {
            fillMethod: "damage-based",
            requiredFor: "ultimate moves",
            drainTriggers: ["time", "not dealing damage"],
            description: description,
            requirements: {
              "C_ultimate": "75%",
              "V_ultimate": "100%"
            }
          }
        };
      }
    }
    
    // Fallback: Look for Admin Panel mention
    if (wikitext.includes('Admin Panel')) {
      return {
        gravitationalForce: {
          fillMethod: "damage-based",
          requiredFor: "ultimate moves",
          drainTriggers: ["time", "not dealing damage"],
          description: "The Gravity fruit uses a Gravitational Force meter that fills through dealing damage. When filled to 75%, it enables the ultimate C move, and when filled to 100%, it enables the ultimate V move.",
          requirements: {
            "C_ultimate": "75%",
            "V_ultimate": "100%"
          }
        }
      };
    }
    
    return null;
  }

  private extractPassiveAbilities(wikitext: string): Array<{ name: string; description: string; showcaseUrl?: string }> {
    const passives: Array<{ name: string; description: string; showcaseUrl?: string }> = []
    
    // Pattern pour les sections de passifs
    const passivePattern = /\|\s*([^|]+?)\s*\|\s*([\s\S]*?)(?=\|-|\|})/g
    const passiveSection = wikitext.match(/Passive=([\s\S]*?)(?=\|-\|)/)?.[1]
    
    if (passiveSection) {
      let match
      while ((match = passivePattern.exec(passiveSection)) !== null) {
        const name = this.cleanWikitext(match[1])
        const description = this.cleanWikitext(match[2]).replace(/\|/g, ''); // Supprimer les pipes restants
        
        if (name && description && name !== 'Name' && name !== 'Description') {
          passives.push({
            name,
            description,
            showcaseUrl: this.extractShowcaseUrl(match[2])
          })
        }
      }
    }
    
    return passives
  }

  private extractMasteryRequirements(wikitext: string): Record<string, number> {
    const masteryReqs: Record<string, number> = {}
    
    // Extract from SkillBox Mas parameters
    const skillPattern = /\{\{SkillBox[^}]*?\|Move\s*=\s*([^|]+)[^}]*?\|Mas\s*=\s*(\d+)/g
    let match
    
    while ((match = skillPattern.exec(wikitext)) !== null) {
      const moveName = this.cleanWikitext(match[1])
      const mastery = parseInt(match[2])
      
      if (moveName && !isNaN(mastery)) {
        masteryReqs[moveName] = mastery
      }
    }
    
    return masteryReqs
  }

  private extractPriceData(wikitext: string, infoboxData: Record<string, string>): any {
    const priceData: any = {
      current: {},
      sources: ["Blox Fruit Dealer", "Shop"]
    }

    // Current prices from infobox
    if (infoboxData.money) {
      priceData.current.money = this.parseNumber(infoboxData.money)
      priceData.current.status = "AVAILABLE"
    }

    // Enhanced Robux extraction with multiple fallbacks
    if (infoboxData.robux) {
      priceData.current.robux = this.parseNumber(infoboxData.robux)
    } else {
      // Fallback: Search for Robux in wikitext patterns (prioritize main price patterns)
      const robuxPatterns = [
        // Priority 1: Infobox patterns
        /\|robux\s*=\s*([0-9,]+)/g,
        // Priority 2: Main description patterns (costs X or Y from dealer)
        /costs.*?\{\{Money\|[0-9,]+\}\}\s*or\s*\{\{Robux\|([0-9,]+)\}\}/g,
        // Priority 3: General Robux templates (but exclude skin prices)
        /\{\{Robux\|([0-9,]+)\}\}(?!\s*\.\))/g, // Exclude patterns ending with .)
        // Priority 4: Generic robux mentions
        /robux.*?([0-9,]+)/gi
      ];

      for (const pattern of robuxPatterns) {
        const matches = [...wikitext.matchAll(pattern)];
        if (matches.length > 0) {
          // For main price patterns, take the first match
          // For skin patterns, skip values that are likely skin prices (< 3000)
          for (const match of matches) {
            const robuxValue = this.parseNumber(match[1] || match[0]);
            if (robuxValue && robuxValue > 0) {
              // Skip likely skin prices for general patterns
              if (pattern.source.includes('Robux') && robuxValue < 3000) {
                continue;
              }
              priceData.current.robux = robuxValue;
              console.log(`✅ Found Robux price via fallback: ${robuxValue}`);
              break;
            }
          }
          if (priceData.current.robux) break;
        }
      }
    }

    // Historical prices from change history
    const historical = this.extractHistoricalPrices(wikitext)
    if (historical.length > 0) {
      priceData.historical = historical
    }

    // Discounts
    const discounts = this.extractDiscounts(wikitext)
    if (discounts.length > 0) {
      priceData.discounts = discounts
    }

    return priceData
  }

  private extractEconomicData(wikitext: string): any {
    return {
      acquisitionMethods: this.extractAcquisitionMethods(wikitext),
      reworkHistory: this.extractReworkHistory(wikitext),
      competitiveRanking: {
        difficulty: this.extractDifficulty(wikitext),
        grinding: this.extractGrindingRating(wikitext),
        pvp: this.extractPvPRating(wikitext)
      },
      marketAnalysis: {
        pricePosition: this.extractPricePosition(wikitext),
        availability: this.extractAvailability(wikitext),
        tradeValue: this.extractTradeValue(wikitext)
      }
    }
  }

  private extractTradeValue(wikitext: string): string {
    const tradePatterns = [
      /holds significant trade value/i,
      /highly valuable.*trading/i,
      /excellent.*trade/i,
      /good for trading/i,
      /decent trade value/i,
      /most expensive.*fruit/i
    ]
    
    for (const pattern of tradePatterns) {
      if (pattern.test(wikitext)) {
        if (pattern.source.includes('significant') || pattern.source.includes('excellent') || pattern.source.includes('most expensive')) {
          return "Very High"
        }
        if (pattern.source.includes('good') || pattern.source.includes('highly valuable')) {
          return "High"
        }
        if (pattern.source.includes('decent')) {
          return "Medium"
        }
      }
    }
    
    return ""
  }

  private extractFormsData(skillBoxData: Record<string, any[]>, statsTableData: Record<string, any[]>): any[] {
    const forms: any[] = []
    
    console.log(`🔍 Processing forms data - Skills: ${Object.keys(skillBoxData).length}, Stats: ${Object.keys(statsTableData).length}`);
    
    for (const [formName, skills] of Object.entries(skillBoxData)) {
      console.log(`  Processing form: ${formName} with ${skills.length} skills`);
      
      const formStatsData = statsTableData[formName] || [];
      console.log(`  Available stats for ${formName}: ${formStatsData.length}`);
      
      const form = {
        name: formName,
        type: this.determineFormType(formName),
        moves: skills.map((skill, index) => {
          const moveName = skill.name || skill.move;
          const moveKey = skill.key;
          
          console.log(`    Processing move ${index + 1}: ${moveName} (key: ${moveKey})`);
          
          const mergedData = {
            key: moveKey || "TAP",
            name: moveName,
            description: skill.description || skill.desc,
            mastery: skill.mastery || skill.mas,
            gif: skill.gif,
            gif1: skill.gif1,
            gif2: skill.gif2,
            // Add stats from stats table if available, and merge effects
            ...this.getStatsForMove(moveName, formStatsData, skill.description || skill.desc, skill)
          };
          
          return mergedData;
        }),
        images: this.extractFormImages(formName, skills, skillBoxData)
      }
      
      forms.push(form)
      console.log(`  ✅ Completed form: ${formName} with ${form.moves.length} moves`);
    }
    
    console.log(`✅ Processed ${forms.length} forms total`);
    return forms
  }

  private extractSkinSystem(wikitext: string): any {
    const skinSystem: any = {
      defaultSkin: "Green",
      craftableSkins: [],
      chromaticSkins: [],
      acquisition: {}
    }
    
    // Extract skin information using {{Skin|...}} templates
    const skinTemplatePattern = /\{\{Skin\|([^}]+)\}\}/g;
    const allSkins: string[] = [];
    let match;
    
    while ((match = skinTemplatePattern.exec(wikitext)) !== null) {
      const skinName = match[1].trim();
      allSkins.push(skinName);
    }
    
    // Categorize skins
    const craftableColors = ["Orange", "Yellow", "Blue", "Red", "Purple", "Black"];
    const chromaticNames = ["Blood Moon", "Eclipse", "Ember", "Phoenix Sky", "Violet Night"];
    
    skinSystem.craftableSkins = allSkins.filter(skin => 
      craftableColors.includes(skin)
    );
    
    skinSystem.chromaticSkins = allSkins.filter(skin => 
      chromaticNames.includes(skin)
    );
    
    // Extract acquisition information from the skin description
    if (wikitext.includes('Barista')) {
      skinSystem.acquisition.crafting = "Barista NPC";
    }
    
    if (wikitext.includes('Robux') && wikitext.includes('Permanent Dragon')) {
      skinSystem.acquisition.purchase = "Robux (permanent required)";
    }
    
    if (wikitext.includes('CHROMATIC') && wikitext.includes('bundle')) {
      skinSystem.acquisition.chromatic = "CHROMATIC bundle in shop";
    }
    
    console.log('🔍 Skin extraction result:', {
      foundSkins: allSkins.length,
      craftableSkins: skinSystem.craftableSkins.length,
      chromaticSkins: skinSystem.chromaticSkins.length,
      hasBarista: wikitext.includes('Barista'),
      hasCHROMATIC: wikitext.includes('CHROMATIC')
    });
    
    return skinSystem;
  }

  private extractChangeHistory(wikitext: string): any[] {
    const changeHistory: any[] = []
    
    const historyPattern = /==Change History==([\s\S]*?)(?===|$)/
    const historyMatch = wikitext.match(historyPattern)
    
    if (historyMatch) {
      const historyContent = historyMatch[1]
      
      // Extract updates
      const updatePattern = /\{\{Update\|(\d+(?:\.\d+)?)\}\}([\s\S]*?)(?=\{\{Update|\}\}$)/g
      let updateMatch
      
      while ((updateMatch = updatePattern.exec(historyContent)) !== null) {
        const updateNumber = updateMatch[1]
        const updateContent = updateMatch[2]
        
        const changes = this.extractUpdateChanges(updateContent)
        
        if (changes.length > 0) {
          changeHistory.push({
            update: updateNumber,
            changes,
            type: this.determineUpdateType(updateContent, changes)
          })
        }
      }
    }
    
    return changeHistory
  }

  // Helper methods
  private hasTransformation(wikitext: string): boolean {
    return wikitext.includes('transform') || wikitext.includes('Imperial Evolution') || wikitext.includes('Fury Meter')
  }

  private extractMainDescription(wikitext: string): string {
    // Multiple patterns for description extraction
    const descPatterns = [
      // Pattern 1: After infobox and quote - improved for Gravity
      /\}\}\s*\{\{Quote[^}]*?\}\}\s*'''([^']+?)'''\s+is\s+a\s+[^.]*?\.\s*([\s\S]*?)(?=\n\n|==|\{\{|__TOC__)/,
      // Pattern 2: After infobox without quote
      /\}\}\s*'''([^']+?)'''\s+is\s+a\s+[^.]*?\.\s*([\s\S]*?)(?=\n\n|==|\{\{|__TOC__)/,
      // Pattern 3: Generic fruit description pattern
      /'''([^']+?)'''\s+(?:is\s+a|allows\s+the\s+user)\s+([\s\S]*?)(?=\n\n|==|\{\{|__TOC__)/,
      // Pattern 4: First paragraph after closing infobox
      /\}\}\s*([^{]*?)(?=\n\n|==|\{\{)/,
      // Pattern 5: Text after specific fruit is mentioned
      /([A-Z][a-z]+)\s+is\s+([^{]*?)(?=\n\n|==|\{\{)/
    ]

    for (const pattern of descPatterns) {
      const match = wikitext.match(pattern)
      if (match) {
        // For patterns that capture both name and description
        if (match[2] && match[2].trim().length > 20) {
          return this.cleanWikitext(match[2])
        }
        // For patterns that capture only description
        if (match[1] && match[1].trim().length > 20) {
          return this.cleanWikitext(match[1])
        }
      }
    }

    return ""
  }

  private extractCombatRating(wikitext: string): any {
    const rating: any = {}
    
    if (wikitext.includes('PvP') || wikitext.includes('PVP')) {
      if (wikitext.includes('excellent') || wikitext.includes('one of the best')) {
        rating.pvp = "Excellent"
      } else if (wikitext.includes('good')) {
        rating.pvp = "Good"
      }
    }
    
    if (wikitext.includes('grinding')) {
      if (wikitext.includes('excellent') || wikitext.includes('one of the best')) {
        rating.grinding = "Excellent"
      } else if (wikitext.includes('good')) {
        rating.grinding = "Good"
      }
    }
    
    return rating
  }

  private extractShopQuote(wikitext: string): string {
    const quotePattern = /\{\{Quote\|([^|]+)\|Shop\}\}/
    const match = wikitext.match(quotePattern)
    
    return match ? this.cleanWikitext(match[1]) : ""
  }

  private extractEnhancedStatsDataWithFruitLogic(wikitext: string): any {
    return this.enhancedStatsExtractor.extractWithFruitSpecificLogic(wikitext);
  }

  private processOverviewData(overviewData: Record<string, { pros: string[]; cons: string[] }>): any {
    // Flatten overview data for main pros/cons
    const allPros: string[] = []
    const allCons: string[] = []

    // Log for debugging
    console.log(`🔍 Processing overview data with ${Object.keys(overviewData).length} forms`)

    for (const [form, data] of Object.entries(overviewData)) {
      console.log(`   Form: ${form} - Pros: ${data.pros.length}, Cons: ${data.cons.length}`)

      // Clean and correct pros/cons before adding
      const cleanedPros = data.pros
        .map(pro => this.cleanWikitext(pro))
        .filter(pro => pro.length > 0)
        .filter(pro => !this.isMisclassifiedAsCon(pro)) // Remove items that should be cons

      const cleanedCons = data.cons
        .map(con => this.cleanWikitext(con))
        .filter(con => con.length > 0)

      // Add misclassified pros to cons
      const misclassifiedPros = data.pros
        .map(pro => this.cleanWikitext(pro))
        .filter(pro => pro.length > 0)
        .filter(pro => this.isMisclassifiedAsCon(pro))

      allPros.push(...cleanedPros)
      allCons.push(...cleanedCons, ...misclassifiedPros)

      // Log corrections
      if (misclassifiedPros.length > 0) {
        console.log(`🔧 Corrected ${misclassifiedPros.length} misclassified pros to cons for ${form}:`, misclassifiedPros)
      }
    }

    const result = {
      pros: [...new Set(allPros)], // Remove duplicates
      cons: [...new Set(allCons)],
      // Keep detailed form-specific data as well (but corrected)
      formSpecific: this.correctFormSpecificData(overviewData)
    }

    console.log(`✅ Final pros: ${result.pros.length}, cons: ${result.cons.length}`)

    return result
  }

  private isMisclassifiedAsCon(text: string): boolean {
    const negativePhrases = [
      'hard to control',
      'difficult to control',
      'vulnerable',
      'weakness',
      'bad for',
      'poor',
      'lacks',
      'cannot',
      'unable to',
      'requires',
      'needs',
      'drains',
      'expensive',
      'high mastery',
      'slow',
      'weak'
    ]

    const lowerText = text.toLowerCase()
    return negativePhrases.some(phrase => lowerText.includes(phrase))
  }

  private correctFormSpecificData(overviewData: Record<string, { pros: string[]; cons: string[] }>): Record<string, { pros: string[]; cons: string[] }> {
    const corrected: Record<string, { pros: string[]; cons: string[] }> = {}

    for (const [form, data] of Object.entries(overviewData)) {
      const cleanedPros = data.pros
        .map(pro => this.cleanWikitext(pro))
        .filter(pro => pro.length > 0)
        .filter(pro => !this.isMisclassifiedAsCon(pro))

      const cleanedCons = data.cons
        .map(con => this.cleanWikitext(con))
        .filter(con => con.length > 0)

      const misclassifiedPros = data.pros
        .map(pro => this.cleanWikitext(pro))
        .filter(pro => pro.length > 0)
        .filter(pro => this.isMisclassifiedAsCon(pro))

      corrected[form] = {
        pros: cleanedPros,
        cons: [...cleanedCons, ...misclassifiedPros]
      }
    }

    return corrected
  }

  private countMoves(wikitext: string): number {
    return (wikitext.match(/\{\{SkillBox/g) || []).length
  }

  private countStats(wikitext: string): number {
    return (wikitext.match(/\{\{Stats Table Row/g) || []).length
  }

  private extractMainImageUrl(wikitext: string, infoboxData: Record<string, string>): string {
    // Dragon-specific image extraction from gallery structure
    const galleryMatch = wikitext.match(/\|image1 = <gallery>\s*([^<]+)/);
    if (galleryMatch) {
      const galleryContent = galleryMatch[1];
      const iconMatch = galleryContent.match(/Dragon\.png\|Icon/);
      if (iconMatch) {
        return `https://static.wikia.nocookie.net/blox-fruits/images/Dragon.png`;
      }
      
      // Fallback to first image in gallery
      const firstImageMatch = galleryContent.match(/([^|\n]+\.(?:png|jpg|jpeg|gif|webp))/i);
      if (firstImageMatch) {
        const filename = firstImageMatch[1].trim();
        return `https://static.wikia.nocookie.net/blox-fruits/images/${encodeURIComponent(filename.replace(/ /g, '_'))}`;
      }
    }
    
    // Try standard infobox fields
    const imageSources = [
      infoboxData.image,
      infoboxData.Image,
      infoboxData.icon,
      infoboxData.Icon,
      infoboxData.image1
    ];
    
    for (const imageSource of imageSources) {
      if (imageSource) {
        const imageMatch = imageSource.match(/([^|]+\.(?:png|jpg|jpeg|gif|webp))/i);
        if (imageMatch) {
          const filename = imageMatch[1].trim();
          return `https://static.wikia.nocookie.net/blox-fruits/images/${encodeURIComponent(filename.replace(/ /g, '_'))}`;
        }
      }
    }
    
    // Fallback: search for Dragon.png specifically
    if (wikitext.includes('Dragon.png')) {
      return `https://static.wikia.nocookie.net/blox-fruits/images/Dragon.png`;
    }
    
    // Final fallback: search for first image in wikitext
    const fallbackImageMatch = wikitext.match(/\[\[File:([^|]+\.(?:png|jpg|jpeg|gif|webp))/i);
    if (fallbackImageMatch) {
      const filename = fallbackImageMatch[1].trim();
      return `https://static.wikia.nocookie.net/blox-fruits/images/${encodeURIComponent(filename.replace(/ /g, '_'))}`;
    }
    
    return "";
  }

  private extractShowcaseUrl(content: string): string | undefined {
    const fileMatch = content.match(/\[\[File:([^|]+)/)
    if (fileMatch) {
      const filename = fileMatch[1].trim()
      return `https://static.wikia.nocookie.net/blox-fruits/images/${encodeURIComponent(filename.replace(/ /g, '_'))}`
    }
    return undefined
  }

  private determineFormType(formName: string): "Normal" | "Hybrid" | "Transformed" {
    if (formName.toLowerCase().includes('hybrid')) return "Hybrid"
    if (formName.toLowerCase().includes('transformed') || formName.toLowerCase().includes('east') || formName.toLowerCase().includes('west')) return "Transformed"
    return "Normal"
  }

  private getStatsForMove(moveName: string, statsData: any[] = [], moveDescription: string = "", skill?: any): any {
    const moveStats: any = this.extractMoveStats(moveDescription);

    // Enhanced matching logic: try multiple approaches
    let tableStats = null;

    if (statsData && statsData.length > 0) {
      // 1. Try exact name match
      tableStats = statsData.find(stat =>
        stat.name?.toLowerCase() === moveName.toLowerCase() ||
        stat.key?.toLowerCase() === moveName.toLowerCase()
      );

      // 2. Try key match with skill key
      if (!tableStats && skill?.key) {
        tableStats = statsData.find(stat =>
          stat.key?.toLowerCase() === skill.key.toLowerCase()
        );
      }

      // 3. Try partial name matching
      if (!tableStats) {
        tableStats = statsData.find(stat => {
          if (!stat.name || !moveName) return false;
          const statName = stat.name.toLowerCase();
          const searchName = moveName.toLowerCase();
          return statName.includes(searchName) || searchName.includes(statName);
        });
      }

      // 4. Try by position if we have a predictable order
      if (!tableStats && skill?.key) {
        const keyMap: Record<string, number> = {
          'TAP': 0, 'Z': 1, 'X': 2, 'C': 3, 'V': 4, 'F': 5
        };
        const index = keyMap[skill.key.toUpperCase()];
        if (index !== undefined && statsData[index]) {
          tableStats = statsData[index];
        }
      }
    }

    if (tableStats) {
      // Check if damage is "unknown" type and try to extract from other sources
      let finalDamage = tableStats.damage;
      if (tableStats.damage && typeof tableStats.damage === 'object' && tableStats.damage.type === 'unknown') {
        // Try to extract damage from move description or other sources
        const extractedDamage = this.extractDamageFromDescription(moveDescription, moveName);
        if (extractedDamage) {
          finalDamage = extractedDamage;
          console.log(`✅ Extracted damage from description for ${moveName}: ${extractedDamage}`);
        }
      }

      // Check if energy is "unknown" type and try to extract from other sources
      let finalEnergy = tableStats.energy;
      if (tableStats.energy && typeof tableStats.energy === 'object' && tableStats.energy.type === 'unknown') {
        const extractedEnergy = this.extractEnergyFromDescription(moveDescription, moveName);
        if (extractedEnergy) {
          finalEnergy = extractedEnergy;
          console.log(`✅ Extracted energy from description for ${moveName}: ${extractedEnergy}`);
        }
      }

      // Merge stats data, preserving skill data priority for certain fields
      Object.assign(moveStats, {
        damage: finalDamage || moveStats.damage,
        cooldown: tableStats.cooldown || moveStats.cooldown,
        energy: finalEnergy || moveStats.energy,
        mastery: skill?.mastery || tableStats.mastery || moveStats.mastery,
        furyMeter: tableStats.furyMeter || moveStats.furyMeter
      });

      console.log(`✅ Merged stats for move: ${moveName} (matched with ${tableStats.name || tableStats.key})`);
    } else {
      console.log(`⚠️ No stats found for move: ${moveName}`);
    }

    // Merge effects from skill if present and not already set
    if (skill && skill.effects && !moveStats.effects) {
      moveStats.effects = skill.effects;
    }

    return moveStats;
  }

  private extractMoveStats(moveDescription: string): any {
    const stats: any = {};
    const damagePatterns = [/deals (\d+) ticks of burn/, /(\d+) damage/];
    const energyPatterns = [/(\d+%?) energy/, /costs (\d+%?) of the user's fury/];

    for (const pattern of damagePatterns) {
      const match = moveDescription.match(pattern);
      if (match) {
        stats.damage = match[1];
        break;
      }
    }

    for (const pattern of energyPatterns) {
      const match = moveDescription.match(pattern);
      if (match) {
        stats.energy = match[1];
        break;
      }
    }

    return stats;
  }

  private extractDamageFromDescription(moveDescription: string, moveName: string): string | number | null {
    // Enhanced damage extraction patterns
    const damagePatterns = [
      // Direct damage mentions
      /deals (\d+(?:\.\d+)?) damage/i,
      /(\d+(?:\.\d+)?) damage/i,
      /dealing (\d+(?:\.\d+)?) damage/i,
      // Burn damage
      /deals (\d+) ticks of burn/i,
      /(\d+) ticks of burn/i,
      // High/massive damage indicators (assign estimated values)
      /very high damage/i,
      /massive damage/i,
      /high damage/i,
      /substantial damage/i,
      /minor damage/i
    ];

    for (const pattern of damagePatterns) {
      const match = moveDescription.match(pattern);
      if (match) {
        if (match[1] && !isNaN(parseFloat(match[1]))) {
          return parseFloat(match[1]);
        }
        // Handle qualitative damage descriptions
        const qualitativeMatch = moveDescription.match(pattern);
        if (qualitativeMatch) {
          const description = qualitativeMatch[0].toLowerCase();
          if (description.includes('very high') || description.includes('massive')) {
            return "Very High";
          } else if (description.includes('high') || description.includes('substantial')) {
            return "High";
          } else if (description.includes('minor')) {
            return "Low";
          }
        }
      }
    }

    // Try to extract from move name patterns (for specific fruits)
    if (moveName.toLowerCase().includes('cannon') || moveName.toLowerCase().includes('beam')) {
      return "High"; // Beam attacks typically have high damage
    }
    if (moveName.toLowerCase().includes('attack') && moveName.toLowerCase().includes('normal')) {
      return "Low"; // Normal attacks typically have lower damage
    }

    return null;
  }

  private extractEnergyFromDescription(moveDescription: string, moveName: string): string | null {
    // Enhanced energy extraction patterns
    const energyPatterns = [
      // Direct energy costs
      /costs (\d+(?:\.\d+)?)%? energy/i,
      /(\d+(?:\.\d+)?)%? energy/i,
      /uses (\d+(?:\.\d+)?)%? energy/i,
      // Fury meter costs
      /costs (\d+(?:\.\d+)?)%? of.*fury/i,
      /(\d+(?:\.\d+)?)%? of.*fury/i,
      /drains (\d+(?:\.\d+)?)%? fury/i,
      // No energy cost indicators
      /doesn't drain.*fury/i,
      /no energy cost/i
    ];

    for (const pattern of energyPatterns) {
      const match = moveDescription.match(pattern);
      if (match) {
        if (match[1] && !isNaN(parseFloat(match[1]))) {
          return `${match[1]}%`;
        }
        // Handle no-cost indicators
        if (pattern.source.includes("doesn't drain") || pattern.source.includes("no energy")) {
          return "0%";
        }
      }
    }

    // Default energy estimates based on move type
    if (moveName.toLowerCase().includes('normal attack') || moveName.toLowerCase().includes('tap')) {
      return "0%"; // Normal attacks typically don't cost energy
    }

    return null;
  }

  private determineUpdateType(content: string, changes: any[]): string {
    if (content.includes('rework') || changes.some(c => c.description.includes('rework'))) {
      return "major_rework"
    }
    if (content.includes('release') || changes.some(c => c.description.includes('release'))) {
      return "release"
    }
    if (changes.some(c => c.type === 'buff' || c.type === 'nerf')) {
      return "balance"
    }
    return "general"
  }

  private extractUpdateChanges(updateContent: string): any[] {
    const changes: any[] = []
    
    const bulletPattern = /\*\s*([^\n]+)/g
    let match
    
    while ((match = bulletPattern.exec(updateContent)) !== null) {
      const description = this.cleanWikitext(match[1])
      let type = "general"
      
      if (description.toLowerCase().includes('buff')) type = "buff"
      else if (description.toLowerCase().includes('nerf')) type = "nerf"
      else if (description.toLowerCase().includes('add')) type = "addition"
      else if (description.toLowerCase().includes('fix')) type = "fix"
      
      changes.push({ description, type })
    }
    
    return changes
  }

  private extractHistoricalPrices(wikitext: string): any[] {
    const history: any[] = [];
    
    // Enhanced patterns for price extraction
    const pricePatterns = [
      // Pattern 1: Direct price changes
      /price was changed from ([\d,]+) to ([\d,]+)/gi,
      // Pattern 2: Update-based price changes
      /Update \d+[^.]*?price[^.]*?([\d,]+)[^.]*?to[^.]*?([\d,]+)/gi,
      // Pattern 3: Historical prices in change history
      /costed[^.]*?([\d,]+)/gi,
      /was[^.]*?([\d,]+)[^.]*?before/gi,
      // Pattern 4: Original prices
      /originally[^.]*?([\d,]+)/gi,
      // Pattern 5: Robux prices
      /Robux.*?([\d,]+)/gi,
      // Pattern 6: Pre-rework prices
      /before.*?rework[^.]*?([\d,]+)/gi
    ];
    
    for (const pattern of pricePatterns) {
      const matches = [...wikitext.matchAll(pattern)];
      
      for (const match of matches) {
        if (match[1] && match[2]) {
          // Two prices found (from X to Y)
          history.push({
            money: this.parseNumber(match[1]),
            newPrice: this.parseNumber(match[2]),
            context: `Changed from ${match[1]} to ${match[2]}`,
            type: 'change'
          });
        } else if (match[1]) {
          // Single price found
          const price = this.parseNumber(match[1]);
          if (price && price > 0) {
            let context = "Historical price";
            let type = "historical";
            
            if (match[0].toLowerCase().includes('original')) {
              context = "Original price";
              type = "original";
            } else if (match[0].toLowerCase().includes('before')) {
              context = "Pre-update price";
              type = "pre-update";
            } else if (match[0].toLowerCase().includes('robux')) {
              context = "Robux price";
              type = "robux";
            }
            
            history.push({
              money: price,
              context,
              type
            });
          }
        }
      }
    }
    
    // Additional extraction from change history section
    const changeHistoryMatch = wikitext.match(/==Change History==([\s\S]*?)(?===|$)/);
    if (changeHistoryMatch) {
      const changeSection = changeHistoryMatch[1];
      const additionalPrices = changeSection.match(/([\d,]+)[^.]*?money/gi) || [];
      
      for (const priceMatch of additionalPrices) {
        const price = this.parseNumber(priceMatch);
        if (price && price > 0 && !history.some(h => h.money === price)) {
          history.push({
            money: price,
            context: "From change history",
            type: "change-history"
          });
        }
      }
    }
    
    // Remove duplicates and sort by price
    const uniqueHistory = history.filter((item, index, arr) => 
      arr.findIndex(h => h.money === item.money && h.type === item.type) === index
    ).sort((a, b) => (a.money || 0) - (b.money || 0));
    
    return uniqueHistory;
  }

  private extractDiscounts(wikitext: string): any[] {
    const discounts: any[] = [];
    const discountPattern = /discount on purchasing the new permanent Dragon \(making it (\d+,\d+) instead of (\d+,\d+)\)/;
    const match = wikitext.match(discountPattern);

    if (match) {
      discounts.push({
        originalPrice: this.parseNumber(match[2]),
        discountedPrice: this.parseNumber(match[1]),
        type: 'robux',
        context: 'Permanent Dragon Token discount'
      });
    }
    return discounts;
  }

  private extractAcquisitionMethods(wikitext: string): any[] {
    const methods: any[] = [];
    const sources = ["Blox Fruit Dealer", "Dragon Egg", "Kitsune Shrine", "Prehistoric Island", "Blox Fruit Gacha", "Shop", "Sea Event"];
    
    sources.forEach(source => {
      if (wikitext.includes(source)) {
        const method: any = { source };
        if (source === "Dragon Egg") {
          method.location = "Prehistoric Island";
          method.chance = "extremely low";
        }
        if (source === "Kitsune Shrine") {
          method.type = "Sea Event";
        }
        methods.push(method);
      }
    });

    return [...new Map(methods.map(item => [item.source, item])).values()];
  }

  private extractReworkHistory(wikitext: string): any {
    const rework: any = {};
    if (wikitext.includes("Dragon Token")) {
      rework.majorUpdate = "Update 24";
      rework.tokenSystem = "Dragon Token conversion";
    }
    const priceIncreaseMatch = wikitext.match(/increased by (\d+\.\d+)%/);
    if (priceIncreaseMatch) {
      rework.priceIncrease = {
        robux: `${priceIncreaseMatch[1]}%`
      };
    }
    return rework;
  }

  private extractDifficulty(wikitext: string): string {
    if (wikitext.match(/most expensive and difficult-to-obtain/)) return "Highest in game";
    if (wikitext.match(/high mastery requirement/i)) return "High";
    if (wikitext.match(/extremely high mastery/i)) return "Very High";
    return "";
  }

  private extractGrindingRating(wikitext: string): string {
    const match = wikitext.match(/grinding potential becomes almost on the same level as (\w+)/);
    if (match) return `S-tier when transformed`;
    if (wikitext.match(/excellent.*grinding/i) || wikitext.match(/good choice for grinding/i)) return "Excellent";
    if (wikitext.match(/good for grinding/i)) return "Good";
    if (wikitext.match(/not recommended.*first sea/i)) return "Good (not for beginners)";
    return "";
  }

  private extractPvPRating(wikitext: string): string {
    if (wikitext.match(/one of the best.*for PvP/i)) return "S-tier when transformed";
    if (wikitext.match(/most powerful and abusable fruits for PvP/i)) return "Excellent";
    if (wikitext.match(/abusable fruits for PvP/i)) return "Excellent";
    if (wikitext.match(/widely regarded.*powerful.*PvP/i)) return "Excellent";
    return "";
  }

  private extractPricePosition(wikitext: string): string {
    if (wikitext.match(/most expensive fruit/i)) return "Most expensive fruit";
    if (wikitext.match(/cheapest.*Mythical.*fruit/i)) return "Cheapest Mythical fruit";
    if (wikitext.match(/least expensive.*Mythical/i)) return "Cheapest Mythical fruit";
    return "";
  }

  private extractAvailability(wikitext: string): string {
    if (wikitext.match(/extremely low chance/i)) return "Extremely rare";
    if (wikitext.match(/very low chance/i)) return "Very rare";
    if (wikitext.match(/made unavailable/i)) return "Previously unavailable";
    if (wikitext.match(/remade available/i)) return "Available";
    return "";
  }

  private extractFormImages(formName: string, skills: any[], allSkillData: Record<string, any[]>): any {
    const images: any = {};
    
    // Extract GIFs from skills for this form
    skills.forEach((skill, index) => {
      const moveName = skill.name || skill.move;
      const moveKey = skill.key || ["TAP", "Z", "X", "C", "V", "F"][index];
      
      // Collect all GIF filenames for this move
      const gifs: string[] = [];
      
      if (skill.gif) {
        gifs.push(skill.gif.replace(/File:/gi, '').trim());
      }
      if (skill.gif1) {
        gifs.push(skill.gif1.replace(/File:/gi, '').trim());
      }
      if (skill.gif2) {
        gifs.push(skill.gif2.replace(/File:/gi, '').trim());
      }
      
      if (gifs.length > 0) {
        images[moveKey] = {
          name: moveName,
          gifs: gifs,
          primary: gifs[0]
        };
      }
    });
    
    // Add form-specific images if available
    if (formName.toLowerCase().includes('transformed')) {
      // Look for transformation images
      const transformImages = this.extractTransformationImages(formName);
      if (transformImages.length > 0) {
        images.transformation = {
          name: `${formName} Transformation`,
          gifs: transformImages,
          primary: transformImages[0]
        };
      }
    }
    
    return images;
  }
  
  
  private extractTransformationImages(formName: string): string[] {
    // This could be enhanced to look for specific transformation GIFs
    // For now, return empty array since we need to search the wikitext more specifically
    return [];
  }

}
